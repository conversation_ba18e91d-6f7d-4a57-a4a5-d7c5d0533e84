"use client";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import styles from "./styles.module.css";

const PreLaunchPage = () => {
  const router = useRouter();

  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const handleClose = () => {
    router.push('/');
  };

  return (
    <>
      <title>SKIDOS - Coming Soon</title>
      <meta name="description" content="Exciting new SKIDOS games coming soon! Get notified when they're available." />
      <div className={styles.preLaunchContainer}>
        {/* Hero Banner Section */}
        <div className={styles.heroBannerSection} ref={(el) => setRef(el)}>
          <div className={styles.bannerContent}>
            {/* Close button */}
            <div className={styles.closeButton} onClick={handleClose}>
              <span className={styles.closeIcon}>×</span>
            </div>
            
            {/* Main content with character and text */}
            <div className={styles.mainContent}>
              <div className={styles.characterSection}>
                <div className={styles.characterContainer}>
                  <Image
                    src="/images/aboutUs/moyo.webp"
                    width={300}
                    height={300}
                    alt="SKIDOS Character"
                    className={styles.characterImage}
                  />
                </div>
              </div>
              
              <div className={styles.textSection}>
                <h1 className={styles.comingSoonText}>
                  COMING SOON
                </h1>
                <div className={styles.ctaButton}>
                  <span className={styles.buttonText}>
                    GET NOTIFIED
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Game Preview Section */}
        <div className={styles.gamePreviewSection} ref={(el) => setRef(el)}>
          <div className={styles.gameCardsContainer}>
            <div className={styles.gameCard}>
              <Image
                src="/images/product/games/abctown.webp"
                width={280}
                height={200}
                alt="ABC Town Game"
                className={styles.gameImage}
              />
              <div className={styles.gameTitle}>
                ABC Town
              </div>
            </div>

            <div className={styles.gameCard}>
              <Image
                src="/images/product/math.webp"
                width={280}
                height={200}
                alt="Math Game"
                className={styles.gameImage}
              />
              <div className={styles.gameTitle}>
                Math
              </div>
            </div>

            <div className={styles.gameCard}>
              <Image
                src="/images/product/games/colorfriends.webp"
                width={280}
                height={200}
                alt="Color Friends Game"
                className={styles.gameImage}
              />
              <div className={styles.gameTitle}>
                Color Friends
              </div>
            </div>

            <div className={styles.gameCard}>
              <Image
                src="/images/product/games/racecars.webp"
                width={280}
                height={200}
                alt="Race Cars Game"
                className={styles.gameImage}
              />
              <div className={styles.gameTitle}>
                Race Cars
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PreLaunchPage;
