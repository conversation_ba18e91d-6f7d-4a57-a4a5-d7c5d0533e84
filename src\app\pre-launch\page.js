"use client";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import styles from "./styles.module.css";

const PreLaunchPage = () => {
  const router = useRouter();

  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const handleClose = () => {
    router.push('/');
  };

  return (
    <>
      <title>SKIDOS x Smurfs - Coming Soon</title>
      <meta name="description" content="Smurfs Are Here to Learn & Play! SKIDOS and the Smurfs have teamed up to bring exciting educational games." />
      <div className={styles.preLaunchContainer}>
        {/* Main Content Section */}
        <div className={styles.mainSection} ref={(el) => setRef(el)}>
          {/* Header with SKIDOS x SMURFS logo */}
          <div className={styles.headerSection}>
            <div className={styles.logoContainer}>
              <h1 className={styles.brandTitle}>
                <span className={styles.skidosLogo}>SKIDOS</span>
                <span className={styles.crossSymbol}>×</span>
                <span className={styles.smurfsLogo}>SMURFS</span>
              </h1>
            </div>
          </div>

          {/* Smurf Characters Section */}
          <div className={styles.charactersSection}>
            <div className={styles.smurfCharacters}>
              <Image
                src="/images/aboutUs/moyo.webp"
                width={200}
                height={200}
                alt="Smurf Character 1"
                className={styles.smurfCharacter1}
              />
              <Image
                src="/images/aboutUs/neta.webp"
                width={200}
                height={200}
                alt="Smurf Character 2"
                className={styles.smurfCharacter2}
              />
            </div>
          </div>

          {/* Main Heading */}
          <div className={styles.mainHeading}>
            <h2 className={styles.primaryHeading}>
              Smurfs Are Here to Learn & Play!
            </h2>
            <p className={styles.subHeading}>
              (Smurf x SKIDOS is coming soon)
            </p>
          </div>

          {/* Description Text */}
          <div className={styles.descriptionSection}>
            <p className={styles.description}>
              Get ready for a magical adventure in learning! SKIDOS and the Smurfs have teamed up to bring your kids exciting educational games with their favorite blue buddies. Fun meets learning like never before!
            </p>
          </div>
        </div>

        {/* Game Screenshots Section */}
        <div className={styles.gameScreenshotsSection} ref={(el) => setRef(el)}>
          <div className={styles.screenshotsContainer}>
            <div className={styles.gameScreenshot}>
              <Image
                src="/images/product/games/brainycity.webp"
                width={350}
                height={250}
                alt="Game Screenshot 1"
                className={styles.screenshotImage}
              />
              <div className={styles.screenshotOverlay}>
                <span className={styles.overlayText}>THE CHARACTERS YOU LOVE!</span>
              </div>
            </div>

            <div className={styles.gameScreenshot}>
              <Image
                src="/images/product/games/fantasyworld.webp"
                width={350}
                height={250}
                alt="Game Screenshot 2"
                className={styles.screenshotImage}
              />
              <div className={styles.screenshotOverlay}>
                <span className={styles.overlayText}>WATCH OUT FOR GARGAMEL!</span>
              </div>
            </div>

            <div className={styles.gameScreenshot}>
              <Image
                src="/images/product/games/miniforest.webp"
                width={350}
                height={250}
                alt="Game Screenshot 3"
                className={styles.screenshotImage}
              />
              <div className={styles.screenshotOverlay}>
                <span className={styles.overlayText}>玩迷你游戏</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PreLaunchPage;
