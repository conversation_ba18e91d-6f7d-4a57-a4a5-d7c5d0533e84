"use client";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import styles from "./styles.module.css";

const PreLaunchPage = () => {
  const router = useRouter();

  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <>
      <title>SKIDOS x Smurfs - Coming Soon</title>
      <meta name="description" content="Smurfs Are Here to Learn & Play! SKIDOS and the Smurfs have teamed up to bring exciting educational games." />
      <div className={styles.preLaunchContainer}>
        {/* Main Content Section */}
        <div className={styles.mainSection} ref={(el) => setRef(el)}>
          {/* Header with SKIDOS x SMURFS logos */}
          <div className={styles.headerSection}>
            <div className={styles.logoContainer}>
              <div className={styles.brandLogos}>
                <Image
                  src="/images/skidosLogo.png"
                  width={200}
                  height={60}
                  alt="SKIDOS Logo"
                  className={styles.skidosLogoImg}
                />
                <span className={styles.crossSymbol}>×</span>
                <Image
                  src="/images/skidosLogo.png"
                  width={200}
                  height={60}
                  alt="SMURFS Logo"
                  className={styles.smurfsLogoImg}
                />
              </div>
            </div>
          </div>

          {/* Smurf Characters Section - Two characters holding hands */}
          <div className={styles.charactersSection}>
            <div className={styles.smurfCharacters}>
              <Image
                src="/images/aboutUs/moyo.webp"
                width={280}
                height={320}
                alt="Smurf Character 1"
                className={styles.smurfCharacter1}
              />
              <Image
                src="/images/aboutUs/neta.webp"
                width={280}
                height={320}
                alt="Smurf Character 2"
                className={styles.smurfCharacter2}
              />
            </div>
          </div>

          {/* Main Heading */}
          <div className={styles.mainHeading}>
            <h1 className={styles.primaryHeading}>
              Smurfs Are Here to Learn & Play!
            </h1>
            <p className={styles.subHeading}>
              (Smurf x SKIDOS is coming soon)
            </p>
          </div>

          {/* Description Text */}
          <div className={styles.descriptionSection}>
            <p className={styles.description}>
              Get ready for a magical adventure in learning! SKIDOS and the<br />
              Smurfs have teamed up to bring your kids exciting educational<br />
              games with their favorite blue buddies. Fun meets learning like<br />
              never before!
            </p>
          </div>
        </div>

        {/* Game Screenshots Section */}
        <div className={styles.gameScreenshotsSection} ref={(el) => setRef(el)}>
          <div className={styles.screenshotsContainer}>
            <div className={styles.gameScreenshot}>
              <Image
                src="/images/product/games/brainycity.webp"
                width={280}
                height={200}
                alt="Game Screenshot 1"
                className={styles.screenshotImage}
              />
              <div className={styles.screenshotOverlay}>
                <span className={styles.overlayText}>THE CHARACTERS YOU LOVE!</span>
              </div>
            </div>

            <div className={styles.gameScreenshot}>
              <Image
                src="/images/product/games/fantasyworld.webp"
                width={280}
                height={200}
                alt="Game Screenshot 2"
                className={styles.screenshotImage}
              />
              <div className={styles.screenshotOverlay}>
                <span className={styles.overlayText}>WATCH OUT FOR GARGAMEL!</span>
              </div>
            </div>

            <div className={styles.gameScreenshot}>
              <Image
                src="/images/product/games/miniforest.webp"
                width={280}
                height={200}
                alt="Game Screenshot 3"
                className={styles.screenshotImage}
              />
              <div className={styles.screenshotOverlay}>
                <span className={styles.overlayText}>玩迷你游戏</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PreLaunchPage;
