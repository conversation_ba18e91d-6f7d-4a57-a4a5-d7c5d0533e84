.preLaunchContainer {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #5B9BD5 0%, #A8C8EC 30%, #D4B5F7 70%, #F0E6FF 100%);
  position: relative;
}

/* Main Section */
.mainSection {
  width: 100%;
  padding: 40px 20px 60px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
}

/* Header Section */
.headerSection {
  margin-bottom: 30px;
  padding-top: 20px;
}

.logoContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.brandLogos {
  display: flex;
  align-items: center;
  gap: 30px;
  justify-content: center;
}

.skidosLogoImg,
.smurfsLogoImg {
  height: auto;
  object-fit: contain;
}

.skidosLogoImg {
  filter: brightness(1.2) saturate(1.3);
}

.smurfsLogoImg {
  filter: brightness(1.1) contrast(1.1);
}

.crossSymbol {
  color: #FFFFFF;
  font-size: 3.5rem;
  font-weight: bold;
  font-family: var(--font-nevermind-bold);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

/* Characters Section */
.charactersSection {
  margin-bottom: 30px;
  position: relative;
}

.smurfCharacters {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  gap: -20px; /* Overlap slightly to simulate holding hands */
  margin-bottom: 20px;
  position: relative;
}

.smurfCharacter1,
.smurfCharacter2 {
  object-fit: contain;
  filter: drop-shadow(0 15px 30px rgba(0, 0, 0, 0.15));
  transition: transform 0.3s ease;
}

.smurfCharacter1 {
  width: 280px;
  height: 320px;
  z-index: 2;
}

.smurfCharacter2 {
  width: 280px;
  height: 320px;
  z-index: 1;
  margin-left: -30px; /* Overlap to simulate connection */
}

/* Main Heading Section */
.mainHeading {
  margin-bottom: 30px;
  max-width: 900px;
}

.primaryHeading {
  font-family: var(--font-nevermind-bold);
  font-size: 3.8rem;
  font-weight: 900;
  line-height: 1.1;
  color: #FF69B4;
  margin: 0 0 8px 0;
  text-align: center;
  letter-spacing: -0.02em;
}

.subHeading {
  font-family: var(--font-poppins);
  font-size: 1.3rem;
  color: #888888;
  margin: 0;
  text-align: center;
  font-weight: 400;
}

/* Description Section */
.descriptionSection {
  margin-bottom: 50px;
  max-width: 800px;
}

.description {
  font-family: var(--font-poppins);
  font-size: 1.4rem;
  font-weight: 500;
  line-height: 1.6;
  color: #666666;
  margin: 0;
  text-align: center;
}

/* Game Screenshots Section */
.gameScreenshotsSection {
  width: 100%;
  padding: 0 20px 40px;
  display: flex;
  justify-content: center;
  background: transparent;
}

.screenshotsContainer {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 15px;
  max-width: 900px;
  width: 100%;
}

.gameScreenshot {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease;
  cursor: pointer;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  flex: 1;
  max-width: 280px;
}

.gameScreenshot:hover {
  transform: translateY(-3px);
}

.screenshotImage {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 8px;
}

.screenshotOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.85) 100%);
  padding: 15px 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlayText {
  font-family: var(--font-nevermind-bold);
  font-size: 1rem;
  font-weight: 900;
  color: #FFFFFF;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1.2;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .skidosLogoImg,
  .smurfsLogoImg {
    width: 180px;
    height: 54px;
  }

  .primaryHeading {
    font-size: 3.4rem;
  }

  .description {
    font-size: 1.3rem;
  }
}

@media (max-width: 992px) {
  .mainSection {
    padding: 30px 20px 50px;
  }

  .brandLogos {
    gap: 20px;
  }

  .skidosLogoImg,
  .smurfsLogoImg {
    width: 160px;
    height: 48px;
  }

  .crossSymbol {
    font-size: 3rem;
  }

  .primaryHeading {
    font-size: 3rem;
  }

  .smurfCharacter1,
  .smurfCharacter2 {
    width: 240px;
    height: 280px;
  }

  .screenshotsContainer {
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .brandLogos {
    flex-direction: column;
    gap: 15px;
  }

  .crossSymbol {
    font-size: 2.5rem;
  }

  .skidosLogoImg,
  .smurfsLogoImg {
    width: 140px;
    height: 42px;
  }

  .primaryHeading {
    font-size: 2.5rem;
  }

  .subHeading {
    font-size: 1.1rem;
  }

  .description {
    font-size: 1.2rem;
  }

  .smurfCharacter1,
  .smurfCharacter2 {
    width: 200px;
    height: 240px;
  }

  .screenshotsContainer {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .gameScreenshot {
    max-width: 300px;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .mainSection {
    padding: 20px 15px 40px;
  }

  .skidosLogoImg,
  .smurfsLogoImg {
    width: 120px;
    height: 36px;
  }

  .crossSymbol {
    font-size: 2rem;
  }

  .primaryHeading {
    font-size: 2rem;
  }

  .subHeading {
    font-size: 1rem;
  }

  .description {
    font-size: 1.1rem;
  }

  .smurfCharacter1,
  .smurfCharacter2 {
    width: 160px;
    height: 200px;
  }

  .overlayText {
    font-size: 0.9rem;
  }
}
