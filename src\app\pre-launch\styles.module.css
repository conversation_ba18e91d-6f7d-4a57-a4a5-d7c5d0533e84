.preLaunchContainer {
  max-width: 1728px;
  margin: 0 auto;
  min-height: 100vh;
  background: linear-gradient(180deg, #6B9EFF 0%, #E8D5FF 100%);
}

/* Main Section */
.mainSection {
  width: 100%;
  padding: 60px 40px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* Header Section */
.headerSection {
  margin-bottom: 40px;
}

.logoContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.brandTitle {
  display: flex;
  align-items: center;
  gap: 20px;
  margin: 0;
  font-size: 4rem;
  font-weight: 900;
  font-family: var(--font-nevermind-bold);
}

.skidosLogo {
  background: linear-gradient(45deg, #4CAF50, #8BC34A, #CDDC39, #FFEB3B, #FF9800, #FF5722);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
}

.crossSymbol {
  color: #FFFFFF;
  font-size: 3rem;
  font-weight: bold;
}

.smurfsLogo {
  color: #FFFFFF;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Characters Section */
.charactersSection {
  margin-bottom: 40px;
}

.smurfCharacters {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  margin-bottom: 40px;
}

.smurfCharacter1,
.smurfCharacter2 {
  width: 200px;
  height: 200px;
  object-fit: contain;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2));
  animation: float 3s ease-in-out infinite;
}

.smurfCharacter2 {
  animation-delay: 1.5s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

/* Main Heading Section */
.mainHeading {
  margin-bottom: 40px;
  max-width: 800px;
}

.primaryHeading {
  font-family: var(--font-nevermind-bold);
  font-size: 4rem;
  font-weight: 900;
  line-height: 1.2;
  color: #EA73C0;
  margin: 0 0 10px 0;
  text-align: center;
}

.subHeading {
  font-family: var(--font-poppins);
  font-size: 1.5rem;
  color: #949494;
  margin: 0;
  text-align: center;
}

/* Description Section */
.descriptionSection {
  margin-bottom: 60px;
  max-width: 900px;
}

.description {
  font-family: var(--font-poppins);
  font-size: 1.8rem;
  font-weight: 500;
  line-height: 1.5;
  color: #86868B;
  margin: 0;
  text-align: center;
}

/* Game Screenshots Section */
.gameScreenshotsSection {
  width: 100%;
  padding: 40px 20px 80px;
  background: #FFFFFF;
  display: flex;
  justify-content: center;
}

.screenshotsContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  max-width: 1200px;
  width: 100%;
  flex-wrap: wrap;
}

.gameScreenshot {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease;
  cursor: pointer;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.gameScreenshot:hover {
  transform: translateY(-5px);
}

.screenshotImage {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 12px;
}

.screenshotOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.8) 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlayText {
  font-family: var(--font-nevermind-bold);
  font-size: 1.2rem;
  font-weight: 900;
  color: #FFFFFF;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .brandTitle {
    font-size: 3.5rem;
  }

  .primaryHeading {
    font-size: 3.5rem;
  }

  .description {
    font-size: 1.6rem;
  }
}

@media (max-width: 992px) {
  .mainSection {
    padding: 40px 20px;
  }

  .brandTitle {
    font-size: 3rem;
    gap: 15px;
  }

  .primaryHeading {
    font-size: 3rem;
  }

  .smurfCharacters {
    gap: 20px;
  }

  .smurfCharacter1,
  .smurfCharacter2 {
    width: 150px;
    height: 150px;
  }

  .screenshotsContainer {
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .brandTitle {
    font-size: 2.5rem;
    gap: 10px;
    flex-direction: column;
  }

  .crossSymbol {
    font-size: 2rem;
  }

  .primaryHeading {
    font-size: 2.5rem;
  }

  .subHeading {
    font-size: 1.2rem;
  }

  .description {
    font-size: 1.4rem;
  }

  .screenshotsContainer {
    flex-direction: column;
    align-items: center;
  }

  .gameScreenshot {
    max-width: 350px;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .mainSection {
    padding: 20px 15px;
  }

  .brandTitle {
    font-size: 2rem;
  }

  .primaryHeading {
    font-size: 2rem;
  }

  .subHeading {
    font-size: 1rem;
  }

  .description {
    font-size: 1.2rem;
  }

  .smurfCharacter1,
  .smurfCharacter2 {
    width: 120px;
    height: 120px;
  }

  .overlayText {
    font-size: 1rem;
  }
}
