.preLaunchContainer {
  max-width: 1728px;
  margin: 0 auto;
  min-height: 100vh;
}

/* Hero Banner Section */
.heroBannerSection {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #FFC3FF 0%, #A500A5 67%);
  position: relative;
  padding: 40px 20px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.heroBannerSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.bannerContent {
  width: 100%;
  max-width: 1280px;
  position: relative;
  height: 100%;
}

.closeButton {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: scale(1.1);
}

.closeIcon {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.mainContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  min-height: 600px;
  gap: 40px;
}

.characterSection {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.characterContainer {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.characterImage {
  width: 100%;
  height: auto;
  max-width: 400px;
  object-fit: contain;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2));
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.textSection {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
}

.comingSoonText {
  font-family: var(--font-nevermind-bold);
  font-size: 8rem;
  font-weight: 900;
  line-height: 1.24;
  text-align: center;
  color: #FFFFFF;
  margin: 0;
  text-shadow: 0px 10px 0px rgba(74, 45, 127, 1);
  letter-spacing: -0.02em;
}

.ctaButton {
  background: linear-gradient(135deg, #9258FE 0%, #CBB1FC 100%);
  border-radius: 50px;
  padding: 20px 60px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0px 10px 0px rgba(74, 45, 127, 1);
  position: relative;
  overflow: hidden;
}

.ctaButton:hover {
  transform: translateY(-2px);
  box-shadow: 0px 12px 0px rgba(74, 45, 127, 1);
}

.ctaButton:active {
  transform: translateY(2px);
  box-shadow: 0px 6px 0px rgba(74, 45, 127, 1);
}

.buttonText {
  font-family: var(--font-poppins);
  font-size: 2.2rem;
  font-weight: 600;
  color: #FFFFFF;
  text-transform: uppercase;
}

/* Game Preview Section */
.gamePreviewSection {
  width: 100%;
  padding: 80px 20px;
  background: #FFFFFF;
  display: flex;
  justify-content: center;
}

.gameCardsContainer {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  gap: 24px;
  max-width: 1280px;
  width: 100%;
}

.gameCard {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0;
  overflow: hidden;
  backdrop-filter: blur(68px);
  transition: transform 0.3s ease;
  cursor: pointer;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  width: 280px;
  height: auto;
}

.gameCard:hover {
  transform: translateY(-5px);
}

.gameImage {
  width: 100%;
  height: auto;
  border-radius: 12px 12px 0 0;
  object-fit: cover;
}

.gameTitle {
  padding: 15px;
  font-family: var(--font-nevermind-bold);
  font-size: 1.4rem;
  font-weight: 900;
  text-align: center;
  color: #333;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .comingSoonText {
    font-size: 6rem;
  }
  
  .buttonText {
    font-size: 1.8rem;
  }
  
  .gameCardsContainer {
    gap: 16px;
  }
}

@media (max-width: 992px) {
  .mainContent {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .comingSoonText {
    font-size: 5rem;
  }
  
  .gameCardsContainer {
    flex-wrap: wrap;
    gap: 20px;
  }
  
  .gameCard {
    flex: 1;
    min-width: 250px;
  }
}

@media (max-width: 768px) {
  .heroBannerSection {
    padding: 20px 15px;
  }
  
  .comingSoonText {
    font-size: 4rem;
  }
  
  .buttonText {
    font-size: 1.5rem;
  }
  
  .ctaButton {
    padding: 15px 40px;
  }
  
  .gameCardsContainer {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .comingSoonText {
    font-size: 3rem;
  }
  
  .buttonText {
    font-size: 1.2rem;
  }
  
  .ctaButton {
    padding: 12px 30px;
  }
  
  .gameCardsContainer {
    flex-direction: column;
    align-items: center;
  }
  
  .gameCard {
    width: 100%;
    max-width: 300px;
  }
  
  .characterImage {
    max-width: 250px;
  }
}
